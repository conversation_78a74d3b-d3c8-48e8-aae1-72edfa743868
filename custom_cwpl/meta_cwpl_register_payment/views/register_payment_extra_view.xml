<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <record id="extra_view_account_payment_register_form" model="ir.ui.view">
    <field name="name">extra.account.payment.register.form</field>
    <field name="model">account.payment.register</field>
    <field name="inherit_id" ref="account.view_account_payment_register_form"/>
    <field name="arch" type="xml">
      <xpath expr="//group[@name='group1']/field[@name='journal_id']" position="before">
        
        <field name="need_vat_ait" attrs="{'invisible': [('partner_type','not in',['customer'])]}"/>
        <field name="vat_ait_other_amount" invisible="1"/>
        <!-- <field name="active_account_move_type"/> -->

      </xpath>

      <xpath expr="//group[@name='group2']" position="after">
        <group name="extra_rp_group" attrs="{'invisible': ['|',('need_vat_ait', '=', False),('partner_type','not in',['customer'])]}">
          
          <!-- <field name="rp_journal" options="{'no_create': True, 'no_create_edit': True}" attrs="{'required': [('need_vat_ait', '=', True)]}"/> -->
          
          <field name="rp_vat"/>
          <field name="rp_vat_account" readonly="1"/>
          
          <field name="rp_ait"/>
          <field name="rp_ait_account" readonly="1"/>

          <field name="rp_other"/>
          <field name="rp_other_account" attrs="{'invisible': [('rp_other', '=', 0)]}" options="{'no_create': True, 'no_create_edit': True}"/>

        </group>
      </xpath>

      <xpath expr="//button[@name='action_create_payments']" position="after">
        <button string="Create VAT AIT Payment" name="action_create_vat_ait_payments" type="object" class="oe_highlight" data-hotkey="t" attrs="{'invisible': ['|',('need_vat_ait', '=', False),('partner_type','not in',['customer'])]}"/>
      </xpath>
    </field>
  </record>
</odoo>