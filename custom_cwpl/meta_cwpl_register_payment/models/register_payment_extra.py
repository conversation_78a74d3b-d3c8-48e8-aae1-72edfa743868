# -*- coding: utf-8 -*-

from odoo import models, fields, api,_
import datetime
import logging
from odoo.exceptions import UserError, ValidationError
_logger=logging.getLogger(__name__)

class ResgisterPaymentExtra(models.TransientModel):
    _inherit = 'account.payment.register'
    
    
    def _default_rp_vat_account(self):
        print("I am Default")
        account_records = self.env['account.account'].search([('code','=','********')])
        return account_records.id
    
    def _default_rp_ait_account(self):
        print("I am Default")
        account_records = self.env['account.account'].search([('code','=','********')])
        return account_records.id
    
    # def _default_active_account_move(self):
    #     _logger.warning("_get_active_account_move------>")
    #     active_acc_move = self.env['account.move'].browse(self._context.get('active_ids', []))
        
    #     return active_acc_move.move_type
    
    
    
    # rp_journal=fields.Many2one(comodel_name="account.journal",string="Extra Payment Journal")
    
    payment_difference = fields.Monetary(compute='_compute_payment_difference')
    
    rp_vat=fields.Monetary(string="VAT Amount")
    rp_vat_account=fields.Many2one(comodel_name="account.account",string="VAT Account",default=lambda self: self._default_rp_vat_account(),readonly=True)
    
    rp_ait=fields.Monetary(string="AIT Amount")
    rp_ait_account=fields.Many2one(comodel_name="account.account",string="AIT Account",default=lambda self: self._default_rp_ait_account(),readonly=True)
    
    rp_other=fields.Monetary(string="Other Amount")
    rp_other_account=fields.Many2one(comodel_name="account.account",string="Other Account",domain="[('user_type_id.name', '=', 'Expenses')]")

    # active_account_move_type = fields.Selection(selection=[
    #         ('entry', 'Journal Entry'),
    #         ('out_invoice', 'Customer Invoice'),
    #         ('out_refund', 'Customer Credit Note'),
    #         ('in_invoice', 'Vendor Bill'),
    #         ('in_refund', 'Vendor Credit Note'),
    #         ('out_receipt', 'Sales Receipt'),
    #         ('in_receipt', 'Purchase Receipt'),
    #     ],string="Active Account Move",compute="_default_active_account_move")
    
    vat_ait_other_amount = fields.Monetary(string="Total",compute="_payment_amout_calculation")
    
    need_vat_ait=fields.Boolean(string="Need VAT,AIT & Others", default=False)
    
    
    @api.depends('rp_vat','rp_ait','rp_other','vat_ait_other_amount')
    def _payment_amout_calculation(self):
        _logger.warning("payment_amout_calculation------>")
        self.vat_ait_other_amount = (self.rp_vat+self.rp_ait+self.rp_other)
        
    @api.depends('amount','rp_vat','rp_ait','rp_other')
    def _compute_payment_difference(self):
        _logger.warning("payment_amout_calculation------>")
        for wizard in self:
            if wizard.source_currency_id == wizard.currency_id:
                # Same currency.
                wizard.payment_difference = (wizard.source_amount_currency - wizard.amount)-(wizard.rp_vat+wizard.rp_ait+wizard.rp_other)
                
            elif wizard.currency_id == wizard.company_id.currency_id:
                # Payment expressed on the company's currency.
                wizard.payment_difference = (wizard.source_amount - wizard.amount)-(wizard.rp_vat+wizard.rp_ait+wizard.rp_other)
            else:
                # Foreign currency on payment different than the one set on the journal entries.
                amount_payment_currency = wizard.company_id.currency_id._convert(wizard.source_amount, wizard.currency_id, wizard.company_id, wizard.payment_date or fields.Date.today())
                wizard.payment_difference = (amount_payment_currency - wizard.amount)-(wizard.rp_vat+wizard.rp_ait+wizard.rp_other)
        
    def action_create_vat_ait_payments(self):
        if self.need_vat_ait==True:
            if self.rp_vat>0.0 or self.rp_ait>0.0:
                _logger.warning("Needs VAT, AIt & Others For Payment--------->")
                ac_move=self.env['account.move']
            
                # currency_name=self.env['res.currency'].search([('name','=','BDT')])
                # current_date=str(datetime.now().date())
                # only_date=datetime.strptime(current_date, "%Y-%m-%d")
                # opening = self.env['account.account'].search([('code','=','********')])
                
                tradable = self.env['account.account'].search([('code','=','********')])
                rp_journal = self.env['account.journal'].search([('id','=',3)])
                
                
                active_move_data=ac_move.browse(self._context.get('active_ids', []))
                cre_pay_list=[]
                print("Payment List------->",cre_pay_list)
                cre_move=[]
                
                for amd in active_move_data:
                    
                    if len(cre_pay_list)<1 and len(cre_move)<1:
                        currency=amd.currency_id
                        acc_values={
                            "ref":f"VAT AIT & Other Adjustment {self.communication}",
                            "journal_id":rp_journal.id,
                            "date":self.payment_date,
                            "move_type":"entry",
                            "state":"draft",
                            "currency_id":currency.id, 
                        }
                        
                        cre_pay=self.action_create_payments()
                        cre_pay_list.append(cre_pay)
                        
                        print("Account Move Values------->",acc_values)
                        ac_move_created=ac_move.create(acc_values)
                        cre_move.append(ac_move_created)
                        print("Account Move Created--------->",ac_move_created)
                        
                        # for rec in range(0,2):
                        #     mv_lines.append(rec)
                            
                        # mv_lines=[]
                        
                        if ac_move_created:
                            ac_move_line=self.env['account.move.line']
                            default_mv_line_vals = ac_move_line.default_get(ac_move_line._fields.keys())
                            if self.rp_other>0 and not self.rp_other<0:
                                mv_lines=[(0,0,dict(
                                    default_mv_line_vals,
                                    move_id=ac_move_created.id,
                                    currency_id=currency.id,
                                    account_id=tradable.id,
                                    partner_id=amd.partner_id.id,
                                    name=self.communication,
                                    debit=0.0,
                                    credit=self.vat_ait_other_amount,
                                    )),
                                    (0,0,dict(
                                    default_mv_line_vals,
                                    move_id=ac_move_created.id,
                                    currency_id=currency.id,
                                    account_id=self.rp_vat_account.id,
                                    partner_id=amd.partner_id.id,
                                    name="VAT AIT & Other Adjustment",
                                    debit=self.rp_vat,
                                    credit=0.0,
                                    )),
                                    (0,0,dict(
                                    default_mv_line_vals,
                                    move_id=ac_move_created.id,
                                    currency_id=currency.id,
                                    account_id=self.rp_ait_account.id,
                                    partner_id=amd.partner_id.id,
                                    name="VAT AIT & Other Adjustment",
                                    debit=self.rp_ait,
                                    credit=0.0,
                                    )),
                                    (0,0,dict(
                                    default_mv_line_vals,
                                    move_id=ac_move_created.id,
                                    currency_id=currency.id,
                                    account_id=self.rp_other_account.id,
                                    partner_id=amd.partner_id.id,
                                    name="VAT AIT & Other Adjustment",
                                    debit=self.rp_other,
                                    credit=0.0,
                                    ))]
                            else:
                                mv_lines=[(0,0,dict(
                                    default_mv_line_vals,
                                    move_id=ac_move_created.id,
                                    currency_id=currency.id,
                                    account_id=tradable.id,
                                    partner_id=amd.partner_id.id,
                                    name=self.communication,
                                    debit=0.0,
                                    credit=self.vat_ait_other_amount,
                                    )),
                                    (0,0,dict(
                                    default_mv_line_vals,
                                    move_id=ac_move_created.id,
                                    currency_id=currency.id,
                                    account_id=self.rp_vat_account.id,
                                    partner_id=amd.partner_id.id,
                                    name="VAT AIT & Other Adjustment",
                                    debit=self.rp_vat,
                                    credit=0.0,
                                    )),
                                    (0,0,dict(
                                    default_mv_line_vals,
                                    move_id=ac_move_created.id,
                                    currency_id=currency.id,
                                    account_id=self.rp_ait_account.id,
                                    partner_id=amd.partner_id.id,
                                    name="VAT AIT & Other Adjustment",
                                    debit=self.rp_ait,
                                    credit=0.0,
                                    ))
                                    ]
                        print("Account Move Line Created--------->",mv_lines)
                        ac_move_created.update(dict(line_ids=mv_lines))
                        amd.amount_residual
                    else:
                        pass
            else:
                _logger.warning("Needs VAT, AIt & Others For Payment--------->")
                ac_move=self.env['account.move']
            
                # currency_name=self.env['res.currency'].search([('name','=','BDT')])
                # current_date=str(datetime.now().date())
                # only_date=datetime.strptime(current_date, "%Y-%m-%d")
                # opening = self.env['account.account'].search([('code','=','********')])
                
                tradable = self.env['account.account'].search([('code','=','********')])
                rp_journal = self.env['account.journal'].search([('id','=',3)])
                
                
                active_move_data=ac_move.browse(self._context.get('active_ids', []))
                cre_pay_list=[]
                print("Payment List------->",cre_pay_list)
                cre_move=[]
                
                for amd in active_move_data:
                    
                    if len(cre_pay_list)<1 and len(cre_move)<1:
                        currency=amd.currency_id
                        acc_values={
                            "ref":f"VAT AIT & Other Adjustment {self.communication}",
                            "journal_id":rp_journal.id,
                            "date":self.payment_date,
                            "move_type":"entry",
                            "state":"draft",
                            "currency_id":currency.id, 
                        }
                        
                        cre_pay=self.action_create_payments()
                        cre_pay_list.append(cre_pay)
                        
                        print("Account Move Values------->",acc_values)
                        ac_move_created=ac_move.create(acc_values)
                        cre_move.append(ac_move_created)
                        print("Account Move Created--------->",ac_move_created)
                        
                        # for rec in range(0,2):
                        #     mv_lines.append(rec)
                            
                        # mv_lines=[]
                        
                        if ac_move_created:
                            ac_move_line=self.env['account.move.line']
                            default_mv_line_vals = ac_move_line.default_get(ac_move_line._fields.keys())
                            if self.rp_other>0 and not self.rp_other<0:
                                mv_lines=[(0,0,dict(
                                    default_mv_line_vals,
                                    move_id=ac_move_created.id,
                                    currency_id=currency.id,
                                    account_id=tradable.id,
                                    partner_id=amd.partner_id.id,
                                    name=self.communication,
                                    debit=0.0,
                                    credit=self.vat_ait_other_amount,
                                    )),
                                    (0,0,dict(
                                    default_mv_line_vals,
                                    move_id=ac_move_created.id,
                                    currency_id=currency.id,
                                    account_id=self.rp_other_account.id,
                                    partner_id=amd.partner_id.id,
                                    name="VAT AIT & Other Adjustment",
                                    debit=self.rp_other,
                                    credit=0.0,
                                    ))]
                            else:
                                raise ValidationError("Other Amount is 0.0. Please Give the value of other amount.")
                        print("Account Move Line Created--------->",mv_lines)
                        ac_move_created.update(dict(line_ids=mv_lines))
                        amd.amount_residual
                    else:
                        pass
        else:
            _logger.warning("Don't Needs VAT, AIt & Others For Payment--------->")
            